import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:gameflex_mobile/theme/app_theme.dart';
import 'package:gameflex_mobile/screens/profile_screen.dart';
import 'package:gameflex_mobile/screens/upload_screen.dart';
import 'package:gameflex_mobile/screens/channel_detail_screen.dart';
import 'package:gameflex_mobile/providers/posts_provider.dart';
import 'package:gameflex_mobile/providers/channels_provider.dart';
import 'package:gameflex_mobile/models/channel_model.dart';
import 'package:gameflex_mobile/widgets/feed.dart';
import 'package:gameflex_mobile/widgets/channel_card.dart';
import 'package:gameflex_mobile/widgets/custom_bottom_navigation.dart';
import 'package:gameflex_mobile/main.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with RouteAware {
  int _selectedIndex = 0;
  int _feedTabIndex =
      1; // 0 for Followed, 1 for Prime (default), 2 for Channels
  late final Widget _feedWidget;

  @override
  void initState() {
    super.initState();
    // Initialize the feed widget once to preserve state
    _feedWidget = const Feed();

    // Load posts when the home screen is first created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);
      if (postsProvider.status == PostsStatus.initial) {
        postsProvider.loadPosts();
      }
      // Start real-time subscriptions
      postsProvider.startRealtimeSubscriptions();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final route = ModalRoute.of(context);
    if (route is PageRoute) {
      MyApp.routeObserver.subscribe(this, route);
    }
  }

  @override
  void dispose() {
    MyApp.routeObserver.unsubscribe(this);
    super.dispose();
  }

  // RouteAware methods
  @override
  void didPopNext() {
    // User came back to this screen from another screen
    // Refresh the feed if we're on the home tab
    print('🔙 HomeScreen: User returned from another screen');
    if (_selectedIndex == 0) {
      print('🔙 HomeScreen: On home tab - refreshing feed');
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);
      postsProvider.refreshPosts();
    }
  }

  @override
  void didPush() {}

  @override
  void didPop() {}

  @override
  void didPushNext() {}

  void _onItemTapped(int index) {
    // If tapping home button while already on home, refresh the feed
    if (index == 0 && _selectedIndex == 0) {
      print(
        '🏠 HomeScreen: Home button tapped while on home - refreshing feed',
      );
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);
      postsProvider.refreshPosts();
      return;
    }

    // If navigating back to home from another tab, refresh the feed
    if (index == 0 && _selectedIndex != 0) {
      print(
        '🏠 HomeScreen: Navigating back to home from tab $_selectedIndex - refreshing feed',
      );
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);
      postsProvider.refreshPosts();
    }

    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:
          _selectedIndex == 0
              ? null
              : AppBar(
                title: Text(
                  _getTabTitle(),
                  style: const TextStyle(
                    color: AppColors.gfGreen,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.search, color: AppColors.gfOffWhite),
                    onPressed: () {},
                  ),
                ],
              ),
      body: _buildBody(),
      bottomNavigationBar: CustomBottomNavigation(
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
      ),
    );
  }

  String _getTabTitle() {
    switch (_selectedIndex) {
      case 1:
        return 'Upload';
      case 2:
        return 'Profile';
      default:
        return 'GameFlex';
    }
  }

  Widget _buildBody() {
    switch (_selectedIndex) {
      case 0:
        return _buildHomeTab();
      case 1:
        return const UploadScreen();
      case 2:
        return const ProfileScreen();
      default:
        return _buildHomeTab();
    }
  }

  Widget _buildHomeTab() {
    return Column(
      children: [
        SafeArea(bottom: false, child: _buildFeedTabs()),
        Expanded(child: _buildTabContent()),
      ],
    );
  }

  Widget _buildTabContent() {
    switch (_feedTabIndex) {
      case 0:
        // Followed tab - show feed (TODO: implement filtering)
        return _feedWidget;
      case 1:
        // Prime tab - show feed (default)
        return _feedWidget;
      case 2:
        // Channels tab - show channels grid
        return _buildChannelsContent();
      default:
        return _feedWidget;
    }
  }

  Widget _buildChannelsContent() {
    return Consumer<ChannelsProvider>(
      builder: (context, channelsProvider, child) {
        // Load channels when first accessing this tab
        if (channelsProvider.channels.isEmpty &&
            channelsProvider.status == ChannelsStatus.initial) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            channelsProvider.loadChannels();
          });
        }

        if (channelsProvider.status == ChannelsStatus.loading &&
            channelsProvider.channels.isEmpty) {
          return const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
            ),
          );
        }

        if (channelsProvider.status == ChannelsStatus.error &&
            channelsProvider.channels.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.red.shade400),
                const SizedBox(height: 16),
                Text(
                  'Failed to load channels',
                  style: Theme.of(
                    context,
                  ).textTheme.headlineSmall?.copyWith(color: Colors.white),
                ),
                const SizedBox(height: 8),
                Text(
                  channelsProvider.errorMessage ?? 'Unknown error',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade400),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () => channelsProvider.loadChannels(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.gfGreen,
                    foregroundColor: AppColors.gfDarkBlue,
                  ),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (channelsProvider.channels.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.forum_outlined, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'No channels found',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Check back later for new channels',
                  style: TextStyle(color: Colors.grey, fontSize: 14),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => channelsProvider.refreshChannels(),
          color: AppColors.gfGreen,
          backgroundColor: AppColors.gfDarkBlue,
          child: GridView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.0,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: channelsProvider.channels.length,
            itemBuilder: (context, index) {
              final channel = channelsProvider.channels[index];
              return ChannelCard(
                channel: channel,
                onTap: () => _onChannelTap(channel),
              );
            },
          ),
        );
      },
    );
  }

  void _onChannelTap(ChannelModel channel) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ChannelDetailScreen(channel: channel),
      ),
    );
  }

  Widget _buildFeedTabs() {
    return Container(
      height: 50,
      decoration: const BoxDecoration(
        color: AppColors.gfDarkBackground100,
        border: Border(
          bottom: BorderSide(color: AppColors.gfGrayBorder, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildTabButton(
              title: 'Followed',
              isSelected: _feedTabIndex == 0,
              onTap: () => _onFeedTabTapped(0),
            ),
          ),
          Container(width: 1, height: 20, color: AppColors.gfGrayBorder),
          Expanded(
            child: _buildTabButton(
              title: 'Prime',
              isSelected: _feedTabIndex == 1,
              onTap: () => _onFeedTabTapped(1),
            ),
          ),
          Container(width: 1, height: 20, color: AppColors.gfGrayBorder),
          Expanded(
            child: _buildTabButton(
              title: 'Channels',
              isSelected: _feedTabIndex == 2,
              onTap: () => _onFeedTabTapped(2),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabButton({
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        height: double.infinity,
        decoration: BoxDecoration(
          color:
              isSelected
                  ? AppColors.gfGreen.withValues(alpha: 0.1)
                  : Colors.transparent,
          border: Border(
            bottom: BorderSide(
              color: isSelected ? AppColors.gfGreen : Colors.transparent,
              width: 2,
            ),
          ),
        ),
        child: Center(
          child: Text(
            title,
            style: TextStyle(
              color: isSelected ? AppColors.gfGreen : AppColors.gfGrayText,
              fontSize: 16,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  void _onFeedTabTapped(int index) {
    setState(() {
      _feedTabIndex = index;
    });
    // TODO: Implement feed filtering logic for Followed (0) tab
  }
}
